import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/services/cart_service.dart';
import '/services/favorites_service.dart';
import '/services/api_service.dart';
import '/services/enhanced_image_service.dart';
import '/utils/firebase_data_initializer.dart';
import 'package:flutter/material.dart';
import 'firebase_product_page_model.dart';
export 'firebase_product_page_model.dart';

class FirebaseProductPageWidget extends StatefulWidget {
  const FirebaseProductPageWidget({
    super.key,
    this.productId,
  });

  final String? productId;

  static String routeName = 'FirebaseProductPage';
  static String routePath = '/product/:productId';

  @override
  State<FirebaseProductPageWidget> createState() =>
      _FirebaseProductPageWidgetState();
}

class _FirebaseProductPageWidgetState extends State<FirebaseProductPageWidget> {
  late FirebaseProductPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();
  final TextEditingController _priceController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => FirebaseProductPageModel());
  }

  @override
  void dispose() {
    _model.dispose();
    _priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get product ID from route parameters if not provided directly
    final productId = widget.productId ??
        GoRouterState.of(context).pathParameters['productId'] ??
        'default-product-id';

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
          automaticallyImplyLeading: false,
          leading: FlutterFlowIconButton(
            borderRadius: 40.0,
            buttonSize: 40.0,
            icon: Icon(
              Icons.arrow_back_ios_rounded,
              color: FlutterFlowTheme.of(context).primaryText,
              size: 24.0,
            ),
            onPressed: () {
              context.pop();
            },
          ),
          title: Text(
            'Product Details',
            style: FlutterFlowTheme.of(context).titleLarge,
          ),
          actions: [
            Padding(
              padding:
                  const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
              child: FlutterFlowIconButton(
                borderRadius: 40.0,
                buttonSize: 40.0,
                icon: Icon(
                  favoritesService.isFavorite(productId)
                      ? Icons.favorite
                      : Icons.favorite_border,
                  color: favoritesService.isFavorite(productId)
                      ? FlutterFlowTheme.of(context).error
                      : FlutterFlowTheme.of(context).primaryText,
                  size: 24.0,
                ),
                onPressed: () {
                  setState(() {
                    favoritesService.toggleFavorite(
                      id: productId,
                      name: 'Product', // Will be updated when we have data
                      imageUrl: '',
                      description: '',
                      price: 0.0,
                    );
                  });
                },
              ),
            ),
          ],
          centerTitle: false,
          elevation: 0.0,
        ),
        body: SafeArea(
          top: true,
          child: FutureBuilder<Map<String, dynamic>?>(
            future: apiService.getProductById(productId),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              } else if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Error loading product: ${snapshot.error}',
                    style: FlutterFlowTheme.of(context).bodyMedium,
                  ),
                );
              } else if (!snapshot.hasData || snapshot.data == null) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Product not found',
                        style: FlutterFlowTheme.of(context).titleMedium,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Please make sure you have initialized the Firebase database with sample data.',
                        style: FlutterFlowTheme.of(context).bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: () async {
                          final scaffoldMessenger =
                              ScaffoldMessenger.of(context);

                          // Initialize sample data
                          scaffoldMessenger.showSnackBar(
                            const SnackBar(
                              content: Text('Initializing sample data...'),
                              duration: Duration(seconds: 2),
                            ),
                          );

                          // Check if still mounted before proceeding
                          if (!mounted) return;

                          try {
                            // Use the initializer
                            await firebaseDataInitializer
                                .initializeAllSampleData();

                            // Refresh the page if still mounted
                            if (mounted) {
                              setState(() {});

                              // Show success message
                              scaffoldMessenger.showSnackBar(
                                const SnackBar(
                                  content: Text(
                                      'Sample data initialized! Refreshing...'),
                                  duration: Duration(seconds: 2),
                                ),
                              );
                            }
                          } catch (e) {
                            if (mounted) {
                              scaffoldMessenger.showSnackBar(
                                SnackBar(
                                  content: Text('Error: $e'),
                                  duration: const Duration(seconds: 3),
                                ),
                              );
                            }
                          }
                        },
                        child: const Text('Initialize Sample Data'),
                      ),
                    ],
                  ),
                );
              }

              // Get product data
              final productData = snapshot.data!;

              // Update model with product data
              _model.minPrice = (productData['minPrice'] ?? 0.0).toDouble();
              _model.maxPrice = (productData['maxPrice'] ?? 0.0).toDouble();
              _model.mainPrice = (productData['mainPrice'] ?? 0.0).toDouble();
              _model.selectedPrice = _model.minPrice;

              // Set initial price in controller
              if (_priceController.text.isEmpty) {
                _priceController.text = _model.minPrice.toString();
              }

              // Get product images - handle both String and Array formats
              List<String> allImages = [];

              // Handle imageUrl field (can be String or Array)
              final imageUrlData = productData['imageUrl'];
              if (imageUrlData is String && imageUrlData.isNotEmpty) {
                allImages.add(imageUrlData);
              } else if (imageUrlData is List && imageUrlData.isNotEmpty) {
                allImages.addAll(imageUrlData.map((img) => img.toString()));
              }

              // Handle additionalImages field
              final List<dynamic> additionalImagesRaw =
                  productData['additionalImages'] ?? [];
              final List<String> additionalImages =
                  additionalImagesRaw.map((img) => img.toString()).toList();

              // Add additional images
              allImages.addAll(additionalImages);

              // Ensure we have at least one placeholder image if no images found
              if (allImages.isEmpty) {
                allImages
                    .add('https://via.placeholder.com/300x300?text=No+Image');
              }

              // No need for a separate function as we handle this in the button

              return SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product Images
                    SizedBox(
                      width: double.infinity,
                      height: 300.0,
                      child: PageView(
                        controller: _model.pageViewController ??=
                            PageController(initialPage: 0),
                        scrollDirection: Axis.horizontal,
                        onPageChanged: (index) {
                          setState(() {
                            _model.currentImageIndex = index;
                          });
                        },
                        children: allImages
                            .map((imageUrl) =>
                                enhancedImageService.buildEnhancedImage(
                                  imageUrl: imageUrl,
                                  width: double.infinity,
                                  height: 300.0,
                                  fit: BoxFit.cover,
                                  placeholder: Container(
                                    width: double.infinity,
                                    height: 300.0,
                                    color:
                                        FlutterFlowTheme.of(context).alternate,
                                    child: Center(
                                      child: CircularProgressIndicator(
                                        color: FlutterFlowTheme.of(context)
                                            .primary,
                                      ),
                                    ),
                                  ),
                                  errorWidget: Container(
                                    width: double.infinity,
                                    height: 300.0,
                                    color:
                                        FlutterFlowTheme.of(context).alternate,
                                    child: Icon(
                                      Icons.image_not_supported,
                                      color: FlutterFlowTheme.of(context)
                                          .secondaryText,
                                      size: 50.0,
                                    ),
                                  ),
                                ))
                            .toList(),
                      ),
                    ),

                    // Page Indicator
                    if (allImages.length > 1)
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: List.generate(
                            allImages.length,
                            (index) => Container(
                              width: 8.0,
                              height: 8.0,
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 4.0),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: _model.currentImageIndex == index
                                    ? FlutterFlowTheme.of(context).primary
                                    : FlutterFlowTheme.of(context).alternate,
                              ),
                            ),
                          ),
                        ),
                      ),

                    // Product Details
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Product Name
                          Text(
                            productData['name'] ?? 'Unknown Product',
                            style: FlutterFlowTheme.of(context).headlineMedium,
                          ),

                          // Main Price
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Row(
                              children: [
                                Text(
                                  'Main Price: ',
                                  style:
                                      FlutterFlowTheme.of(context).bodyMedium,
                                ),
                                Text(
                                  '\$${_model.mainPrice.toStringAsFixed(2)}',
                                  style: FlutterFlowTheme.of(context)
                                      .titleMedium
                                      .copyWith(
                                        color: FlutterFlowTheme.of(context)
                                            .primary,
                                      ),
                                ),
                              ],
                            ),
                          ),

                          // Description
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text(
                              productData['description'] ??
                                  'No description available',
                              style: FlutterFlowTheme.of(context).bodyMedium,
                            ),
                          ),

                          const SizedBox(height: 16.0),

                          // Price Selection
                          Text(
                            'Select Price',
                            style: FlutterFlowTheme.of(context).titleMedium,
                          ),

                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: TextField(
                              controller: _priceController,
                              decoration: InputDecoration(
                                labelText: 'Price',
                                hintText:
                                    'Enter price between \$${_model.minPrice} and \$${_model.maxPrice}',
                                border: const OutlineInputBorder(),
                                prefixIcon: const Icon(Icons.attach_money),
                                helperText:
                                    'Current price: \$${_model.selectedPrice.toStringAsFixed(2)}',
                                helperStyle: FlutterFlowTheme.of(context)
                                    .bodySmall
                                    .copyWith(
                                      color:
                                          FlutterFlowTheme.of(context).primary,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                              keyboardType: TextInputType.number,
                              onChanged: (value) {
                                try {
                                  double price = double.parse(value);
                                  setState(() {
                                    if (price >= _model.minPrice &&
                                        price <= _model.maxPrice) {
                                      _model.selectedPrice = price;
                                    }
                                  });
                                } catch (e) {
                                  // Handle parsing error
                                }
                              },
                            ),
                          ),

                          // Earnings Information
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Container(
                              padding: const EdgeInsets.all(12.0),
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context)
                                    .primaryBackground,
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Your Earnings',
                                    style:
                                        FlutterFlowTheme.of(context).titleSmall,
                                  ),
                                  const SizedBox(height: 8.0),
                                  Text(
                                    'You will earn \$${(_model.selectedPrice - _model.mainPrice).toStringAsFixed(2)} from this sale',
                                    style:
                                        FlutterFlowTheme.of(context).bodyMedium,
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Add to Cart Button
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16.0),
                            child: FFButtonWidget(
                              onPressed: () {
                                // Add to cart
                                cartService.addItem(
                                  productId: productId,
                                  name:
                                      productData['name'] ?? 'Unknown Product',
                                  imageUrl: allImages.isNotEmpty
                                      ? allImages.first
                                      : '',
                                  selectedPrice: _model.selectedPrice,
                                  color: 'Default',
                                  mainPrice: _model.mainPrice,
                                );

                                // Show success message
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Added to cart'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                              },
                              text: 'Add to Cart',
                              options: FFButtonOptions(
                                width: double.infinity,
                                height: 50.0,
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 0.0),
                                iconPadding:
                                    const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 0.0, 0.0, 0.0),
                                color: FlutterFlowTheme.of(context).primary,
                                textStyle: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .copyWith(
                                      color: FlutterFlowTheme.of(context)
                                          .primaryBackground,
                                    ),
                                elevation: 2.0,
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
