import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/index.dart';
import '/services/favorites_service.dart';
import '/services/enhanced_image_service.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'allitems_model.dart';
export 'allitems_model.dart';

class AllitemsWidget extends StatefulWidget {
  const AllitemsWidget({super.key});

  static String routeName = 'allitems';
  static String routePath = '/allitems';

  @override
  State<AllitemsWidget> createState() => _AllitemsWidgetState();
}

class _AllitemsWidgetState extends State<AllitemsWidget> {
  late AllitemsModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  // Using the global favorites service instance from services/favorites_service.dart

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => AllitemsModel());
  }

  @override
  void dispose() {
    _model.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          automaticallyImplyLeading: false,
          leading: FlutterFlowIconButton(
            borderRadius: 8.0,
            buttonSize: 40.0,
            icon: Icon(
              Icons.arrow_back_ios_rounded,
              color: FlutterFlowTheme.of(context).primaryText,
              size: 24.0,
            ),
            onPressed: () {
              context.pop();
            },
          ),
          title: Text(
            'All Items',
            style: FlutterFlowTheme.of(context).headlineMedium.override(
                  font: GoogleFonts.interTight(
                    fontWeight: FontWeight.bold,
                    fontStyle:
                        FlutterFlowTheme.of(context).headlineMedium.fontStyle,
                  ),
                  letterSpacing: 0.0,
                  fontWeight: FontWeight.bold,
                  fontStyle:
                      FlutterFlowTheme.of(context).headlineMedium.fontStyle,
                ),
          ),
          actions: const [],
          centerTitle: false,
          elevation: 0.0,
        ),
        body: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 24.0),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'All Products',
                      style: FlutterFlowTheme.of(context).titleMedium.override(
                            font: GoogleFonts.interTight(
                              fontWeight: FontWeight.w600,
                              fontStyle: FlutterFlowTheme.of(context)
                                  .titleMedium
                                  .fontStyle,
                            ),
                            letterSpacing: 0.0,
                            fontWeight: FontWeight.w600,
                            fontStyle: FlutterFlowTheme.of(context)
                                .titleMedium
                                .fontStyle,
                          ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Icon(
                          Icons.shopping_bag_outlined,
                          color: FlutterFlowTheme.of(context).primary,
                          size: 20.0,
                        ),
                      ].divide(const SizedBox(width: 4.0)),
                    ),
                  ],
                ),
                GridView(
                  padding: EdgeInsets.zero,
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16.0,
                    mainAxisSpacing: 16.0,
                    childAspectRatio: 0.7,
                  ),
                  primary: false,
                  shrinkWrap: true,
                  scrollDirection: Axis.vertical,
                  children: [
                    _buildProductItem(
                      image:
                          'https://images.unsplash.com/photo-1598327105666-5b89351aff97?q=80&w=1000',
                      title: 'Premium Smartphone',
                      price: '\$899',
                      description: 'Latest model with high-end features',
                      onTap: () =>
                          context.pushNamed(ProdcutpageWidget.routeName),
                    ),
                    _buildProductItem(
                      image:
                          'https://images.unsplash.com/photo-1606220588913-b3aacb4d2f46?q=80&w=1000',
                      title: 'Wireless Headphones',
                      price: '\$249',
                      description: 'Noise-cancelling with 30hr battery',
                      onTap: () =>
                          context.pushNamed(ProdcutpageWidget.routeName),
                    ),
                    _buildProductItem(
                      image:
                          'https://images.unsplash.com/photo-1542291026-7eec264c27ff?q=80&w=1000',
                      title: 'Running Sneakers',
                      price: '\$129',
                      description: 'Lightweight with cushioned support',
                      onTap: () =>
                          context.pushNamed(ProdcutpageWidget.routeName),
                    ),
                    _buildProductItem(
                      image:
                          'https://images.unsplash.com/photo-1523275335684-37898b6baf30?q=80&w=1000',
                      title: 'Smart Watch',
                      price: '\$199',
                      description: 'Fitness and health tracking',
                      onTap: () =>
                          context.pushNamed(ProdcutpageWidget.routeName),
                    ),
                    _buildProductItem(
                      image:
                          'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?q=80&w=1000',
                      title: 'Ultrabook Pro',
                      price: '\$1,299',
                      description: 'High performance laptop',
                      onTap: () =>
                          context.pushNamed(ProdcutpageWidget.routeName),
                    ),
                    _buildProductItem(
                      image:
                          'https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f?q=80&w=1000',
                      title: 'Digital Camera',
                      price: '\$899',
                      description: 'Professional photography',
                      onTap: () =>
                          context.pushNamed(ProdcutpageWidget.routeName),
                    ),
                  ],
                ),
              ].divide(const SizedBox(height: 24.0)),
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build product items
  Widget _buildProductItem({
    required String image,
    required String title,
    required String price,
    required VoidCallback onTap,
    String description = 'Premium quality product',
  }) {
    // Generate a unique ID for the product based on title
    final String productId = title.toLowerCase().replaceAll(' ', '-');
    // Convert price string to double (remove $ and commas)
    final double priceValue =
        double.parse(price.replaceAll('\$', '').replaceAll(',', ''));

    // Check if product is already in favorites
    final bool isInFavorites = favoritesService.isFavorite(productId);

    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: 160,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product image with favorite button
              Stack(
                children: [
                  ClipRRect(
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(12)),
                    child: enhancedImageService.buildEnhancedImage(
                      imageUrl: image,
                      width: double.infinity,
                      height: 120,
                      fit: BoxFit.cover,
                      placeholder: Container(
                        width: double.infinity,
                        height: 120,
                        color: Colors.grey[200],
                        child: Center(
                          child: CircularProgressIndicator(
                            color: FlutterFlowTheme.of(context).primary,
                          ),
                        ),
                      ),
                      errorWidget: Container(
                        width: double.infinity,
                        height: 120,
                        color: Colors.grey[200],
                        child: const Icon(Icons.image_not_supported),
                      ),
                    ),
                  ),
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(200),
                        shape: BoxShape.circle,
                      ),
                      child: InkWell(
                        onTap: () {
                          // Toggle favorite status
                          final bool wasInFavorites = isInFavorites;

                          // Toggle the favorite
                          favoritesService.toggleFavorite(
                            id: productId,
                            name: title,
                            imageUrl: image,
                            description: description,
                            price: priceValue,
                          );

                          // Force UI update
                          setState(() {});

                          // Show confirmation message
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                wasInFavorites
                                    ? 'Removed from favorites'
                                    : 'Added to favorites',
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Inter',
                                      color: FlutterFlowTheme.of(context).info,
                                    ),
                              ),
                              duration: const Duration(seconds: 2),
                              backgroundColor:
                                  FlutterFlowTheme.of(context).primary,
                            ),
                          );
                        },
                        child: Icon(
                          isInFavorites
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: isInFavorites
                              ? FlutterFlowTheme.of(context).error
                              : FlutterFlowTheme.of(context).secondaryText,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              // Product details
              Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            font: GoogleFonts.inter(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: FlutterFlowTheme.of(context).bodySmall.override(
                            font: GoogleFonts.inter(),
                            color: Colors.grey,
                          ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      price,
                      style: FlutterFlowTheme.of(context).titleMedium.override(
                            font: GoogleFonts.inter(
                              fontWeight: FontWeight.bold,
                            ),
                            color: const Color(0xFF6C5CE7),
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
